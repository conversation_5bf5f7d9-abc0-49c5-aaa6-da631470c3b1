import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { Pet, PetWithProfile } from '../types/database'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

export const usePets = () => {
  const [pets, setPets] = useState<PetWithProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  const fetchPets = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('pets')
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) {
        setError(error.message)
        toast.error('Error al cargar las mascotas')
      } else {
        setPets(data || [])
        setError(null)
      }
    } catch (err) {
      setError('Error inesperado al cargar las mascotas')
      toast.error('Error inesperado')
    } finally {
      setLoading(false)
    }
  }

  const fetchMyPets = async () => {
    if (!user) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('pets')
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        setError(error.message)
        toast.error('Error al cargar tus mascotas')
      } else {
        setPets(data || [])
        setError(null)
      }
    } catch (err) {
      setError('Error inesperado al cargar tus mascotas')
      toast.error('Error inesperado')
    } finally {
      setLoading(false)
    }
  }

  const createPet = async (petData: Omit<Pet, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) {
      toast.error('Debes estar autenticado')
      return { error: new Error('No authenticated') }
    }

    try {
      const { data, error } = await supabase
        .from('pets')
        .insert([
          {
            ...petData,
            user_id: user.id,
          }
        ])
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .single()

      if (error) {
        toast.error('Error al crear la publicación')
        return { error: new Error(error.message) }
      }

      // Agregar la nueva mascota al estado
      setPets(prev => [data, ...prev])
      toast.success('¡Publicación creada exitosamente!')
      return { data, error: null }
    } catch (err) {
      toast.error('Error inesperado al crear la publicación')
      return { error: err as Error }
    }
  }

  const updatePet = async (petId: string, updates: Partial<Pet>) => {
    try {
      const { data, error } = await supabase
        .from('pets')
        .update(updates)
        .eq('id', petId)
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .single()

      if (error) {
        toast.error('Error al actualizar la publicación')
        return { error: new Error(error.message) }
      }

      // Actualizar el estado
      setPets(prev => prev.map(pet => pet.id === petId ? data : pet))
      toast.success('Publicación actualizada')
      return { data, error: null }
    } catch (err) {
      toast.error('Error inesperado al actualizar')
      return { error: err as Error }
    }
  }

  const deletePet = async (petId: string) => {
    try {
      const { error } = await supabase
        .from('pets')
        .delete()
        .eq('id', petId)

      if (error) {
        toast.error('Error al eliminar la publicación')
        return { error: new Error(error.message) }
      }

      // Remover del estado
      setPets(prev => prev.filter(pet => pet.id !== petId))
      toast.success('Publicación eliminada')
      return { error: null }
    } catch (err) {
      toast.error('Error inesperado al eliminar')
      return { error: err as Error }
    }
  }

  const togglePetStatus = async (petId: string) => {
    try {
      // Primero obtener el estado actual
      const currentPet = pets.find(pet => pet.id === petId)
      if (!currentPet) return { error: new Error('Mascota no encontrada') }

      const newStatus = !currentPet.is_active

      const { error } = await supabase
        .from('pets')
        .update({ is_active: newStatus })
        .eq('id', petId)

      if (error) {
        toast.error('Error al cambiar el estado')
        return { error: new Error(error.message) }
      }

      // Actualizar el estado local
      setPets(prev => prev.map(pet => 
        pet.id === petId ? { ...pet, is_active: newStatus } : pet
      ))

      toast.success(newStatus ? 'Publicación activada' : 'Publicación desactivada')
      return { error: null }
    } catch (err) {
      toast.error('Error inesperado')
      return { error: err as Error }
    }
  }

  useEffect(() => {
    fetchPets()
  }, [])

  return {
    pets,
    loading,
    error,
    fetchPets,
    fetchMyPets,
    createPet,
    updatePet,
    deletePet,
    togglePetStatus,
    refetch: fetchPets,
  }
}
