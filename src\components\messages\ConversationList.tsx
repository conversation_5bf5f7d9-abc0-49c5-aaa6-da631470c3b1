import React from 'react'
import { ConversationWithDetails } from '../../types/database'
import { useAuth } from '../../contexts/AuthContext'
import { User, MessageCircle } from 'lucide-react'
import { format } from 'date-fns'

interface ConversationListProps {
  conversations: ConversationWithDetails[]
  currentConversation: ConversationWithDetails | null
  onSelectConversation: (conversation: ConversationWithDetails) => void
  loading: boolean
}

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  currentConversation,
  onSelectConversation,
  loading,
}) => {
  const { user } = useAuth()

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">Cargando conversaciones...</p>
        </div>
      </div>
    )
  }

  if (conversations.length === 0) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center">
          <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay conversaciones
          </h3>
          <p className="text-gray-500 text-sm">
            Las conversaciones aparecerán aquí cuando contactes con otros usuarios
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Mensajes</h2>
      </div>
      
      <div className="divide-y divide-gray-200">
        {conversations.map((conversation) => {
          // Determinar quién es el otro usuario en la conversación
          const otherUser = conversation.owner_id === user?.id 
            ? conversation.interested_user 
            : conversation.owner

          // Obtener el último mensaje
          const lastMessage = conversation.messages && conversation.messages.length > 0
            ? conversation.messages[conversation.messages.length - 1]
            : null

          // Contar mensajes no leídos
          const unreadCount = conversation.messages?.filter(
            msg => msg.sender_id !== user?.id && msg.status === 'sent'
          ).length || 0

          const isSelected = currentConversation?.id === conversation.id

          return (
            <button
              key={conversation.id}
              onClick={() => onSelectConversation(conversation)}
              className={`w-full p-4 text-left hover:bg-gray-50 transition-colors ${
                isSelected ? 'bg-primary-50 border-r-2 border-primary-500' : ''
              }`}
            >
              <div className="flex items-start space-x-3">
                {/* Avatar del otro usuario */}
                <div className="flex-shrink-0">
                  {otherUser.avatar_url ? (
                    <img
                      src={otherUser.avatar_url}
                      alt={otherUser.full_name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  {/* Información del usuario y mascota */}
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {otherUser.full_name}
                    </p>
                    {lastMessage && (
                      <p className="text-xs text-gray-500">
                        {format(new Date(lastMessage.created_at), 'HH:mm')}
                      </p>
                    )}
                  </div>

                  {/* Nombre de la mascota */}
                  <p className="text-xs text-gray-500 mb-1">
                    Sobre: {conversation.pets.name}
                  </p>

                  {/* Último mensaje */}
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600 truncate">
                      {lastMessage ? (
                        <>
                          {lastMessage.sender_id === user?.id && (
                            <span className="text-gray-400">Tú: </span>
                          )}
                          {lastMessage.content}
                        </>
                      ) : (
                        <span className="text-gray-400 italic">
                          Conversación iniciada
                        </span>
                      )}
                    </p>

                    {/* Badge de mensajes no leídos */}
                    {unreadCount > 0 && (
                      <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary-600 rounded-full">
                        {unreadCount}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Información de la mascota */}
              <div className="mt-2 ml-13">
                <div className="flex items-center space-x-2">
                  {conversation.pets.photo_url && (
                    <img
                      src={conversation.pets.photo_url}
                      alt={conversation.pets.name}
                      className="w-6 h-6 rounded object-cover"
                    />
                  )}
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    conversation.pets.status === 'lost'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {conversation.pets.status === 'lost' ? 'Perdido' : 'Adopción'}
                  </span>
                </div>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}
