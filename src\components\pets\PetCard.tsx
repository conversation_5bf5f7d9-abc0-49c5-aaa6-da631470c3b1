import React from 'react'
import { PetWithProfile } from '../../types/database'
import { MapPin, Calendar, MessageCircle, Edit, Trash2, User, Heart, Search } from 'lucide-react'
import { format } from 'date-fns'
import { useAuth } from '../../contexts/AuthContext'

interface PetCardProps {
  pet: PetWithProfile
  onEdit?: (pet: PetWithProfile) => void
  onDelete?: (petId: string) => void
  onContact?: (pet: PetWithProfile) => void
  showActions?: boolean
}

export const PetCard: React.FC<PetCardProps> = ({ 
  pet, 
  onEdit, 
  onDelete, 
  onContact, 
  showActions = false 
}) => {
  const { user } = useAuth()
  const isOwner = user?.id === pet.user_id

  const getStatusInfo = (status: 'lost' | 'adoption') => {
    switch (status) {
      case 'lost':
        return {
          label: 'Perdido',
          icon: Search,
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          iconColor: 'text-red-600'
        }
      case 'adoption':
        return {
          label: 'En Adopción',
          icon: Heart,
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          iconColor: 'text-green-600'
        }
    }
  }

  const statusInfo = getStatusInfo(pet.status)
  const StatusIcon = statusInfo.icon

  return (
    <div className="card hover:shadow-lg transition-shadow duration-200">
      {/* Imagen */}
      <div className="relative">
        {pet.photo_url ? (
          <img
            src={pet.photo_url}
            alt={pet.name}
            className="w-full h-48 object-cover rounded-t-lg"
          />
        ) : (
          <div className="w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center">
            <User className="h-16 w-16 text-gray-400" />
          </div>
        )}
        
        {/* Badge de estado */}
        <div className={`absolute top-3 left-3 ${statusInfo.bgColor} ${statusInfo.textColor} px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1`}>
          <StatusIcon className={`h-4 w-4 ${statusInfo.iconColor}`} />
          {statusInfo.label}
        </div>

        {/* Badge de inactivo si corresponde */}
        {!pet.is_active && (
          <div className="absolute top-3 right-3 bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Inactivo
          </div>
        )}
      </div>

      <div className="card-content">
        {/* Título y descripción */}
        <div className="mb-4">
          <h3 className="text-xl font-bold text-gray-900 mb-2">{pet.name}</h3>
          <p className="text-gray-600 text-sm line-clamp-3">{pet.description}</p>
        </div>

        {/* Información de ubicación y fecha */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-gray-500 text-sm">
            <MapPin className="h-4 w-4 mr-2" />
            {pet.location}
          </div>
          <div className="flex items-center text-gray-500 text-sm">
            <Calendar className="h-4 w-4 mr-2" />
            {format(new Date(pet.created_at), 'dd/MM/yyyy')}
          </div>
        </div>

        {/* Información del usuario */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center">
            {pet.profiles.avatar_url ? (
              <img
                src={pet.profiles.avatar_url}
                alt={pet.profiles.full_name}
                className="w-8 h-8 rounded-full object-cover mr-3"
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                <User className="h-4 w-4 text-gray-400" />
              </div>
            )}
            <div>
              <p className="text-sm font-medium text-gray-900">{pet.profiles.full_name}</p>
              <p className="text-xs text-gray-500">@{pet.profiles.username}</p>
            </div>
          </div>

          {/* Acciones */}
          <div className="flex items-center gap-2">
            {showActions && isOwner ? (
              <>
                <button
                  onClick={() => onEdit?.(pet)}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                  title="Editar"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onDelete?.(pet.id)}
                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                  title="Eliminar"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </>
            ) : (
              !isOwner && (
                <button
                  onClick={() => onContact?.(pet)}
                  className="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors"
                  title="Contactar"
                >
                  <MessageCircle className="h-4 w-4" />
                </button>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
