import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { usePets } from '../hooks/usePets'
import { PetCard } from '../components/pets/PetCard'
import { 
  Plus, 
  Search, 
  Heart, 
  MessageCircle, 
  TrendingUp,
  PawPrint,
  Users,
  MapPin
} from 'lucide-react'

export const DashboardPage: React.FC = () => {
  const { profile } = useAuth()
  const { pets, loading } = usePets()
  const [myPetsCount, setMyPetsCount] = useState(0)

  // Obtener estadísticas
  const recentPets = pets.slice(0, 6)
  const lostPets = pets.filter(pet => pet.status === 'lost').length
  const adoptionPets = pets.filter(pet => pet.status === 'adoption').length

  useEffect(() => {
    if (profile) {
      const userPets = pets.filter(pet => pet.user_id === profile.id)
      setMyPetsCount(userPets.length)
    }
  }, [pets, profile])

  const stats = [
    {
      name: 'Mascotas Perdidas',
      value: lostPets,
      icon: Search,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      name: 'En Adopción',
      value: adoptionPets,
      icon: Heart,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Mis Publicaciones',
      value: myPetsCount,
      icon: PawPrint,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Total Activas',
      value: pets.length,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ]

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Bienvenida */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          ¡Hola, {profile?.full_name}! 👋
        </h1>
        <p className="text-gray-600">
          Bienvenido a PetSite ARG. Aquí puedes ver las últimas publicaciones y gestionar tus mascotas.
        </p>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.name} className="card">
              <div className="card-content">
                <div className="flex items-center">
                  <div className={`${stat.bgColor} p-3 rounded-lg`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Acciones rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Link
          to="/pets/new"
          className="card hover:shadow-lg transition-shadow cursor-pointer group"
        >
          <div className="card-content">
            <div className="flex items-center">
              <div className="bg-primary-100 p-3 rounded-lg group-hover:bg-primary-200 transition-colors">
                <Plus className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Nueva Publicación</h3>
                <p className="text-gray-600">Publica una mascota perdida o en adopción</p>
              </div>
            </div>
          </div>
        </Link>

        <Link
          to="/pets"
          className="card hover:shadow-lg transition-shadow cursor-pointer group"
        >
          <div className="card-content">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-lg group-hover:bg-green-200 transition-colors">
                <Search className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Buscar Mascotas</h3>
                <p className="text-gray-600">Explora todas las publicaciones</p>
              </div>
            </div>
          </div>
        </Link>

        <Link
          to="/messages"
          className="card hover:shadow-lg transition-shadow cursor-pointer group"
        >
          <div className="card-content">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-lg group-hover:bg-blue-200 transition-colors">
                <MessageCircle className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Mensajes</h3>
                <p className="text-gray-600">Revisa tus conversaciones</p>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Publicaciones recientes */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Publicaciones Recientes</h2>
          <Link
            to="/pets"
            className="text-primary-600 hover:text-primary-700 font-medium flex items-center gap-1"
          >
            Ver todas
            <TrendingUp className="h-4 w-4" />
          </Link>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="card animate-pulse">
                <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                <div className="card-content">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : recentPets.length === 0 ? (
          <div className="text-center py-12">
            <PawPrint className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No hay publicaciones aún
            </h3>
            <p className="text-gray-600 mb-6">
              Sé el primero en publicar una mascota en PetSite ARG
            </p>
            <Link to="/pets/new" className="btn-primary">
              Crear Primera Publicación
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentPets.map((pet) => (
              <PetCard
                key={pet.id}
                pet={pet}
                showActions={false}
              />
            ))}
          </div>
        )}
      </div>

      {/* Información adicional */}
      <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              ¿Necesitas ayuda?
            </h3>
            <p className="text-gray-600">
              Consulta nuestras guías sobre cómo publicar mascotas y usar la plataforma
            </p>
          </div>
          <div className="hidden sm:block">
            <div className="bg-primary-100 p-3 rounded-full">
              <Users className="h-8 w-8 text-primary-600" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
