import React, { useState } from 'react'
import { usePets } from '../hooks/usePets'
import { PetCard } from '../components/pets/PetCard'
import { PetForm } from '../components/pets/PetForm'
import { PetWithProfile } from '../types/database'
import { Plus, Search, Filter, Loader2, Heart, AlertCircle } from 'lucide-react'

export const PetsPage: React.FC = () => {
  const { pets, loading, createPet, updatePet, deletePet } = usePets()
  const [showForm, setShowForm] = useState(false)
  const [editingPet, setEditingPet] = useState<PetWithProfile | null>(null)
  const [filter, setFilter] = useState<'all' | 'lost' | 'adoption'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Filtrar mascotas
  const filteredPets = pets.filter(pet => {
    const matchesFilter = filter === 'all' || pet.status === filter
    const matchesSearch = pet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.location.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const handleCreatePet = async (data: any) => {
    setIsSubmitting(true)
    try {
      const { error } = await createPet(data)
      if (!error) {
        setShowForm(false)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleUpdatePet = async (data: any) => {
    if (!editingPet) return
    
    setIsSubmitting(true)
    try {
      const { error } = await updatePet(editingPet.id, data)
      if (!error) {
        setEditingPet(null)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeletePet = async (petId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta publicación?')) {
      await deletePet(petId)
    }
  }

  const handleEditPet = (pet: PetWithProfile) => {
    setEditingPet(pet)
    setShowForm(false)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingPet(null)
  }

  const handleContact = (pet: PetWithProfile) => {
    // TODO: Implementar navegación al chat
    console.log('Contactar con:', pet.profiles.username)
  }

  if (showForm || editingPet) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <PetForm
          pet={editingPet || undefined}
          onSubmit={editingPet ? handleUpdatePet : handleCreatePet}
          onCancel={handleCancelForm}
          isLoading={isSubmitting}
        />
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mascotas</h1>
          <p className="text-gray-600">Encuentra mascotas perdidas o en adopción</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="btn-primary flex items-center gap-2 mt-4 md:mt-0"
        >
          <Plus className="h-4 w-4" />
          Nueva Publicación
        </button>
      </div>

      {/* Filtros y búsqueda */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Búsqueda */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Buscar por nombre, descripción o ubicación..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10 w-full"
              />
            </div>
          </div>

          {/* Filtros */}
          <div className="flex gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                filter === 'all'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Todos
            </button>
            <button
              onClick={() => setFilter('lost')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-1 ${
                filter === 'lost'
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Search className="h-4 w-4" />
              Perdidos
            </button>
            <button
              onClick={() => setFilter('adoption')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-1 ${
                filter === 'adoption'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Heart className="h-4 w-4" />
              Adopción
            </button>
          </div>
        </div>
      </div>

      {/* Contenido */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary-600 mb-4" />
            <p className="text-gray-600">Cargando mascotas...</p>
          </div>
        </div>
      ) : filteredPets.length === 0 ? (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm || filter !== 'all' ? 'No se encontraron mascotas' : 'No hay publicaciones aún'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filter !== 'all' 
              ? 'Intenta cambiar los filtros o términos de búsqueda'
              : 'Sé el primero en publicar una mascota'
            }
          </p>
          {!searchTerm && filter === 'all' && (
            <button
              onClick={() => setShowForm(true)}
              className="btn-primary flex items-center gap-2 mx-auto"
            >
              <Plus className="h-4 w-4" />
              Crear Primera Publicación
            </button>
          )}
        </div>
      ) : (
        <>
          {/* Contador de resultados */}
          <div className="mb-6">
            <p className="text-gray-600">
              {filteredPets.length} {filteredPets.length === 1 ? 'mascota encontrada' : 'mascotas encontradas'}
            </p>
          </div>

          {/* Grid de mascotas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPets.map((pet) => (
              <PetCard
                key={pet.id}
                pet={pet}
                onEdit={handleEditPet}
                onDelete={handleDeletePet}
                onContact={handleContact}
                showActions={true}
              />
            ))}
          </div>
        </>
      )}
    </div>
  )
}
