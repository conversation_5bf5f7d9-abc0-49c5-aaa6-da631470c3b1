import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Pet } from '../../types/database'
import { X, Save, Upload, MapPin, FileText, Tag, Heart, Search } from 'lucide-react'

const petSchema = z.object({
  name: z.string()
    .min(1, 'El nombre es requerido')
    .max(100, 'El nombre no puede tener más de 100 caracteres'),
  description: z.string()
    .min(10, 'La descripción debe tener al menos 10 caracteres')
    .max(2000, 'La descripción no puede tener más de 2000 caracteres'),
  location: z.string()
    .min(3, 'La ubicación debe tener al menos 3 caracteres')
    .max(200, 'La ubicación no puede tener más de 200 caracteres'),
  status: z.enum(['lost', 'adoption'], {
    required_error: 'Debes seleccionar un estado'
  }),
  photo_url: z.string().url('URL inválida').optional().or(z.literal('')),
})

type PetFormData = z.infer<typeof petSchema>

interface PetFormProps {
  pet?: Pet
  onSubmit: (data: PetFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export const PetForm: React.FC<PetFormProps> = ({ 
  pet, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}) => {
  const isEditing = !!pet

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<PetFormData>({
    resolver: zodResolver(petSchema),
    defaultValues: pet ? {
      name: pet.name,
      description: pet.description,
      location: pet.location,
      status: pet.status,
      photo_url: pet.photo_url || '',
    } : {
      name: '',
      description: '',
      location: '',
      status: 'lost',
      photo_url: '',
    },
  })

  const watchedStatus = watch('status')
  const watchedPhotoUrl = watch('photo_url')

  const statusOptions = [
    {
      value: 'lost',
      label: 'Perdido',
      description: 'Mi mascota se perdió y la estoy buscando',
      icon: Search,
      color: 'text-red-600',
      bgColor: 'bg-red-50 border-red-200',
    },
    {
      value: 'adoption',
      label: 'En Adopción',
      description: 'Busco un hogar responsable para esta mascota',
      icon: Heart,
      color: 'text-green-600',
      bgColor: 'bg-green-50 border-green-200',
    },
  ] as const

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">
            {isEditing ? 'Editar Publicación' : 'Nueva Publicación'}
          </h2>
          <button
            onClick={onCancel}
            className="btn-outline flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancelar
          </button>
        </div>
      </div>

      <div className="card-content">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Nombre */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nombre de la Mascota *
            </label>
            <div className="relative">
              <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                {...register('name')}
                className="input pl-10"
                placeholder="Ej: Max, Luna, Firulais..."
              />
            </div>
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
            )}
          </div>

          {/* Estado */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Estado de la Mascota *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {statusOptions.map((option) => {
                const Icon = option.icon
                return (
                  <label
                    key={option.value}
                    className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                      watchedStatus === option.value
                        ? `${option.bgColor} border-2`
                        : 'bg-white border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <input
                      {...register('status')}
                      type="radio"
                      value={option.value}
                      className="sr-only"
                    />
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Icon className={`h-6 w-6 ${option.color}`} />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {option.label}
                        </div>
                        <div className="text-sm text-gray-500">
                          {option.description}
                        </div>
                      </div>
                    </div>
                  </label>
                )
              })}
            </div>
            {errors.status && (
              <p className="text-red-500 text-sm mt-1">{errors.status.message}</p>
            )}
          </div>

          {/* Descripción */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Descripción *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                {...register('description')}
                rows={4}
                className="input pl-10 resize-none"
                placeholder="Describe a la mascota: raza, color, tamaño, características especiales, dónde se vio por última vez, etc."
              />
            </div>
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
            )}
          </div>

          {/* Ubicación */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Ubicación *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                {...register('location')}
                className="input pl-10"
                placeholder="Ej: Palermo, CABA - Argentina"
              />
            </div>
            {errors.location && (
              <p className="text-red-500 text-sm mt-1">{errors.location.message}</p>
            )}
          </div>

          {/* Foto */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Foto (opcional)
            </label>
            <div className="relative">
              <Upload className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                {...register('photo_url')}
                className="input pl-10"
                placeholder="https://ejemplo.com/foto-mascota.jpg"
              />
            </div>
            {errors.photo_url && (
              <p className="text-red-500 text-sm mt-1">{errors.photo_url.message}</p>
            )}
            
            {/* Preview de la imagen */}
            {watchedPhotoUrl && (
              <div className="mt-3">
                <p className="text-sm text-gray-600 mb-2">Vista previa:</p>
                <img
                  src={watchedPhotoUrl}
                  alt="Vista previa"
                  className="w-32 h-32 object-cover rounded-lg border"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              </div>
            )}
          </div>

          {/* Botón de envío */}
          <div className="flex justify-end pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isLoading 
                ? (isEditing ? 'Actualizando...' : 'Publicando...') 
                : (isEditing ? 'Actualizar' : 'Publicar')
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
