-- =====================================================
-- PETSITE ARG - ESTRUCTURA COMPLETA DE BASE DE DATOS
-- =====================================================
-- Este archivo contiene toda la estructura SQL necesaria
-- para crear las tablas, tipos y políticas RLS en Supabase
-- =====================================================

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TIPOS ENUMERADOS
-- =====================================================

-- Tipo para el estado de las mascotas
CREATE TYPE pet_status AS ENUM ('lost', 'adoption');

-- Tipo para el estado de los mensajes
CREATE TYPE message_status AS ENUM ('sent', 'read');

-- =====================================================
-- TABLAS
-- =====================================================

-- Tabla de perfiles de usuario
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT username_length CHECK (char_length(username) >= 3 AND char_length(username) <= 30),
    CONSTRAINT username_format CHECK (username ~ '^[a-zA-Z0-9_]+$'),
    CONSTRAINT full_name_length CHECK (char_length(full_name) >= 2 AND char_length(full_name) <= 100)
);

-- Tabla de mascotas (publicaciones)
CREATE TABLE public.pets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    photo_url TEXT,
    location TEXT NOT NULL,
    status pet_status NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT name_length CHECK (char_length(name) >= 1 AND char_length(name) <= 100),
    CONSTRAINT description_length CHECK (char_length(description) >= 10 AND char_length(description) <= 2000),
    CONSTRAINT location_length CHECK (char_length(location) >= 3 AND char_length(location) <= 200)
);

-- Tabla de conversaciones (para agrupar mensajes)
CREATE TABLE public.conversations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    interested_user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraint para evitar conversaciones duplicadas
    UNIQUE(pet_id, owner_id, interested_user_id)
);

-- Tabla de mensajes
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE NOT NULL,
    sender_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    status message_status DEFAULT 'sent' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT content_length CHECK (char_length(content) >= 1 AND char_length(content) <= 1000)
);

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- =====================================================

-- Índices para búsquedas frecuentes
CREATE INDEX idx_pets_user_id ON public.pets(user_id);
CREATE INDEX idx_pets_status ON public.pets(status);
CREATE INDEX idx_pets_active ON public.pets(is_active);
CREATE INDEX idx_pets_created_at ON public.pets(created_at DESC);

CREATE INDEX idx_conversations_pet_id ON public.conversations(pet_id);
CREATE INDEX idx_conversations_owner_id ON public.conversations(owner_id);
CREATE INDEX idx_conversations_interested_user_id ON public.conversations(interested_user_id);

CREATE INDEX idx_messages_conversation_id ON public.messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);

-- =====================================================
-- FUNCIONES AUXILIARES
-- =====================================================

-- Función para actualizar el campo updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Triggers para actualizar updated_at automáticamente
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pets_updated_at 
    BEFORE UPDATE ON public.pets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON public.conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- HABILITAR ROW LEVEL SECURITY (RLS)
-- =====================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS RLS - PROFILES
-- =====================================================

-- Los usuarios pueden ver todos los perfiles (para mostrar info de publicadores)
CREATE POLICY "Profiles are viewable by everyone"
    ON public.profiles FOR SELECT
    USING (true);

-- Los usuarios solo pueden insertar su propio perfil
CREATE POLICY "Users can insert their own profile"
    ON public.profiles FOR INSERT
    WITH CHECK (auth.uid() = id);

-- Los usuarios solo pueden actualizar su propio perfil
CREATE POLICY "Users can update their own profile"
    ON public.profiles FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Los usuarios solo pueden eliminar su propio perfil
CREATE POLICY "Users can delete their own profile"
    ON public.profiles FOR DELETE
    USING (auth.uid() = id);

-- =====================================================
-- POLÍTICAS RLS - PETS
-- =====================================================

-- Todos pueden ver las mascotas activas
CREATE POLICY "Pets are viewable by everyone"
    ON public.pets FOR SELECT
    USING (is_active = true);

-- Los usuarios autenticados pueden crear mascotas
CREATE POLICY "Authenticated users can create pets"
    ON public.pets FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Solo el dueño puede actualizar sus mascotas
CREATE POLICY "Users can update their own pets"
    ON public.pets FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Solo el dueño puede eliminar sus mascotas
CREATE POLICY "Users can delete their own pets"
    ON public.pets FOR DELETE
    USING (auth.uid() = user_id);

-- =====================================================
-- POLÍTICAS RLS - CONVERSATIONS
-- =====================================================

-- Solo los participantes pueden ver la conversación
CREATE POLICY "Users can view their own conversations"
    ON public.conversations FOR SELECT
    USING (
        auth.uid() = owner_id OR
        auth.uid() = interested_user_id
    );

-- Los usuarios autenticados pueden crear conversaciones
CREATE POLICY "Authenticated users can create conversations"
    ON public.conversations FOR INSERT
    TO authenticated
    WITH CHECK (
        auth.uid() = interested_user_id AND
        auth.uid() != owner_id
    );

-- Solo los participantes pueden actualizar la conversación
CREATE POLICY "Participants can update conversations"
    ON public.conversations FOR UPDATE
    USING (
        auth.uid() = owner_id OR
        auth.uid() = interested_user_id
    )
    WITH CHECK (
        auth.uid() = owner_id OR
        auth.uid() = interested_user_id
    );

-- =====================================================
-- POLÍTICAS RLS - MESSAGES
-- =====================================================

-- Solo los participantes de la conversación pueden ver los mensajes
CREATE POLICY "Users can view messages in their conversations"
    ON public.messages FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.conversations
            WHERE conversations.id = messages.conversation_id
            AND (
                conversations.owner_id = auth.uid() OR
                conversations.interested_user_id = auth.uid()
            )
        )
    );

-- Solo los participantes pueden enviar mensajes
CREATE POLICY "Participants can send messages"
    ON public.messages FOR INSERT
    TO authenticated
    WITH CHECK (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM public.conversations
            WHERE conversations.id = conversation_id
            AND (
                conversations.owner_id = auth.uid() OR
                conversations.interested_user_id = auth.uid()
            )
        )
    );

-- Solo el emisor puede actualizar sus mensajes (para marcar como leído)
CREATE POLICY "Users can update their own messages"
    ON public.messages FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.conversations
            WHERE conversations.id = messages.conversation_id
            AND (
                conversations.owner_id = auth.uid() OR
                conversations.interested_user_id = auth.uid()
            )
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.conversations
            WHERE conversations.id = conversation_id
            AND (
                conversations.owner_id = auth.uid() OR
                conversations.interested_user_id = auth.uid()
            )
        )
    );

-- =====================================================
-- FUNCIÓN PARA CREAR PERFIL AUTOMÁTICAMENTE
-- =====================================================

-- Función que se ejecuta cuando se registra un nuevo usuario
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, username, full_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'Usuario')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger que ejecuta la función cuando se crea un usuario
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- COMENTARIOS FINALES
-- =====================================================

-- Este esquema proporciona:
-- 1. Autenticación segura con auth.users de Supabase
-- 2. Perfiles de usuario con usernames únicos
-- 3. Sistema de mascotas perdidas/adopción
-- 4. Mensajería interna segura
-- 5. Políticas RLS que garantizan la seguridad
-- 6. Índices para optimizar consultas
-- 7. Triggers para mantener datos actualizados
-- 8. Creación automática de perfiles

-- Para usar este esquema:
-- 1. Copia todo el contenido
-- 2. Pégalo en el SQL Editor de Supabase
-- 3. Ejecuta el script
-- 4. ¡Tu backend estará listo!
