import React, { useState } from 'react'
import { useMessages } from '../hooks/useMessages'
import { ConversationList } from '../components/messages/ConversationList'
import { ChatWindow } from '../components/messages/ChatWindow'
import { ConversationWithDetails } from '../types/database'

export const MessagesPage: React.FC = () => {
  const {
    conversations,
    currentConversation,
    messages,
    loading,
    messagesLoading,
    sendMessage,
    selectConversation,
  } = useMessages()

  const [showChat, setShowChat] = useState(false)

  const handleSelectConversation = async (conversation: ConversationWithDetails) => {
    await selectConversation(conversation)
    setShowChat(true)
  }

  const handleSendMessage = async (content: string) => {
    if (currentConversation) {
      await sendMessage(currentConversation.id, content)
    }
  }

  const handleBackToList = () => {
    setShowChat(false)
  }

  return (
    <div className="h-screen flex bg-white">
      {/* Lista de conversaciones - Desktop siempre visible, Mobile condicional */}
      <div className={`w-full md:w-80 border-r border-gray-200 ${
        showChat ? 'hidden md:block' : 'block'
      }`}>
        <ConversationList
          conversations={conversations}
          currentConversation={currentConversation}
          onSelectConversation={handleSelectConversation}
          loading={loading}
        />
      </div>

      {/* Ventana de chat - Desktop siempre visible, Mobile condicional */}
      <div className={`flex-1 ${
        showChat ? 'block' : 'hidden md:block'
      }`}>
        <ChatWindow
          conversation={currentConversation}
          messages={messages}
          onSendMessage={handleSendMessage}
          onBack={handleBackToList}
          loading={messagesLoading}
        />
      </div>
    </div>
  )
}
