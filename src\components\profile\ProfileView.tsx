import React from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { User, Mail, Calendar, Edit } from 'lucide-react'
import { format } from 'date-fns'

interface ProfileViewProps {
  onEdit: () => void
}

export const ProfileView: React.FC<ProfileViewProps> = ({ onEdit }) => {
  const { user, profile } = useAuth()

  if (!user || !profile) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No se pudo cargar el perfil</p>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Mi Perfil</h2>
          <button
            onClick={onEdit}
            className="btn-outline flex items-center gap-2"
          >
            <Edit className="h-4 w-4" />
            Editar
          </button>
        </div>
      </div>

      <div className="card-content">
        <div className="flex flex-col items-center text-center mb-8">
          {/* Avatar */}
          <div className="relative mb-4">
            {profile.avatar_url ? (
              <img
                src={profile.avatar_url}
                alt={profile.full_name}
                className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
              />
            ) : (
              <div className="w-24 h-24 rounded-full bg-primary-100 flex items-center justify-center border-4 border-white shadow-lg">
                <User className="h-12 w-12 text-primary-600" />
              </div>
            )}
          </div>

          {/* Información básica */}
          <h3 className="text-2xl font-bold text-gray-900 mb-1">
            {profile.full_name}
          </h3>
          <p className="text-gray-600 mb-4">@{profile.username}</p>
        </div>

        {/* Detalles del perfil */}
        <div className="space-y-4">
          <div className="flex items-center p-4 bg-gray-50 rounded-lg">
            <Mail className="h-5 w-5 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-700">Email</p>
              <p className="text-gray-900">{user.email}</p>
            </div>
          </div>

          <div className="flex items-center p-4 bg-gray-50 rounded-lg">
            <User className="h-5 w-5 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-700">Nombre de Usuario</p>
              <p className="text-gray-900">@{profile.username}</p>
            </div>
          </div>

          <div className="flex items-center p-4 bg-gray-50 rounded-lg">
            <Calendar className="h-5 w-5 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-700">Miembro desde</p>
              <p className="text-gray-900">
                {format(new Date(profile.created_at), 'dd/MM/yyyy')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
