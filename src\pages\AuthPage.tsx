import React, { useState } from 'react'
import { LoginForm } from '../components/auth/LoginForm'
import { RegisterForm } from '../components/auth/RegisterForm'
import { Heart, PawPrint } from 'lucide-react'

export const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true)

  const toggleMode = () => {
    setIsLogin(!isLogin)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Lado izquierdo - Información de la app */}
        <div className="text-center lg:text-left">
          <div className="flex items-center justify-center lg:justify-start mb-6">
            <div className="bg-primary-600 p-3 rounded-full mr-3">
              <PawPrint className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">PetSite ARG</h1>
          </div>
          
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Encuentra y Ayuda a las Mascotas
          </h2>
          
          <p className="text-xl text-gray-600 mb-8">
            La plataforma que conecta a las mascotas perdidas con sus familias 
            y facilita adopciones responsables en toda Argentina.
          </p>

          <div className="space-y-4">
            <div className="flex items-center justify-center lg:justify-start">
              <div className="bg-green-100 p-2 rounded-full mr-3">
                <Heart className="h-5 w-5 text-green-600" />
              </div>
              <span className="text-gray-700">Publica mascotas perdidas o en adopción</span>
            </div>
            
            <div className="flex items-center justify-center lg:justify-start">
              <div className="bg-blue-100 p-2 rounded-full mr-3">
                <PawPrint className="h-5 w-5 text-blue-600" />
              </div>
              <span className="text-gray-700">Conecta con otros amantes de los animales</span>
            </div>
            
            <div className="flex items-center justify-center lg:justify-start">
              <div className="bg-purple-100 p-2 rounded-full mr-3">
                <Heart className="h-5 w-5 text-purple-600" />
              </div>
              <span className="text-gray-700">Mensajería segura entre usuarios</span>
            </div>
          </div>
        </div>

        {/* Lado derecho - Formularios */}
        <div className="w-full">
          {isLogin ? (
            <LoginForm onToggleMode={toggleMode} />
          ) : (
            <RegisterForm onToggleMode={toggleMode} />
          )}
        </div>
      </div>
    </div>
  )
}
