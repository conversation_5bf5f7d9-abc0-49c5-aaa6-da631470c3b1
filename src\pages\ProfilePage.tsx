import React, { useState } from 'react'
import { ProfileView } from '../components/profile/ProfileView'
import { ProfileEdit } from '../components/profile/ProfileEdit'

export const ProfilePage: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false)

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    setIsEditing(false)
  }

  const handleSave = () => {
    setIsEditing(false)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      {isEditing ? (
        <ProfileEdit onCancel={handleCancel} onSave={handleSave} />
      ) : (
        <ProfileView onEdit={handleEdit} />
      )}
    </div>
  )
}
