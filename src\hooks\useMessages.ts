import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { ConversationWithDetails, MessageWithSender } from '../types/database'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

export const useMessages = () => {
  const [conversations, setConversations] = useState<ConversationWithDetails[]>([])
  const [currentConversation, setCurrentConversation] = useState<ConversationWithDetails | null>(null)
  const [messages, setMessages] = useState<MessageWithSender[]>([])
  const [loading, setLoading] = useState(true)
  const [messagesLoading, setMessagesLoading] = useState(false)
  const { user } = useAuth()

  // Obtener todas las conversaciones del usuario
  const fetchConversations = async () => {
    if (!user) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          pets (
            id,
            name,
            photo_url,
            status
          ),
          owner:profiles!conversations_owner_id_fkey (
            id,
            username,
            full_name,
            avatar_url
          ),
          interested_user:profiles!conversations_interested_user_id_fkey (
            id,
            username,
            full_name,
            avatar_url
          ),
          messages (
            id,
            content,
            status,
            created_at,
            sender_id
          )
        `)
        .or(`owner_id.eq.${user.id},interested_user_id.eq.${user.id}`)
        .order('updated_at', { ascending: false })

      if (error) {
        toast.error('Error al cargar conversaciones')
        console.error('Error fetching conversations:', error)
      } else {
        setConversations(data || [])
      }
    } catch (err) {
      toast.error('Error inesperado al cargar conversaciones')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  // Obtener mensajes de una conversación específica
  const fetchMessages = async (conversationId: string) => {
    try {
      setMessagesLoading(true)
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (error) {
        toast.error('Error al cargar mensajes')
        console.error('Error fetching messages:', error)
      } else {
        setMessages(data || [])
      }
    } catch (err) {
      toast.error('Error inesperado al cargar mensajes')
      console.error('Error:', err)
    } finally {
      setMessagesLoading(false)
    }
  }

  // Crear o obtener una conversación existente
  const getOrCreateConversation = async (petId: string, ownerId: string) => {
    if (!user) {
      toast.error('Debes estar autenticado')
      return null
    }

    if (user.id === ownerId) {
      toast.error('No puedes contactarte a ti mismo')
      return null
    }

    try {
      // Primero verificar si ya existe una conversación
      const { data: existingConversation, error: fetchError } = await supabase
        .from('conversations')
        .select('*')
        .eq('pet_id', petId)
        .eq('owner_id', ownerId)
        .eq('interested_user_id', user.id)
        .single()

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error checking existing conversation:', fetchError)
        toast.error('Error al verificar conversación')
        return null
      }

      if (existingConversation) {
        return existingConversation.id
      }

      // Si no existe, crear una nueva
      const { data: newConversation, error: createError } = await supabase
        .from('conversations')
        .insert([
          {
            pet_id: petId,
            owner_id: ownerId,
            interested_user_id: user.id,
          }
        ])
        .select('*')
        .single()

      if (createError) {
        console.error('Error creating conversation:', createError)
        toast.error('Error al crear conversación')
        return null
      }

      // Actualizar la lista de conversaciones
      await fetchConversations()
      
      return newConversation.id
    } catch (err) {
      console.error('Error in getOrCreateConversation:', err)
      toast.error('Error inesperado')
      return null
    }
  }

  // Enviar un mensaje
  const sendMessage = async (conversationId: string, content: string) => {
    if (!user) {
      toast.error('Debes estar autenticado')
      return { error: new Error('Not authenticated') }
    }

    if (!content.trim()) {
      toast.error('El mensaje no puede estar vacío')
      return { error: new Error('Empty message') }
    }

    try {
      const { data, error } = await supabase
        .from('messages')
        .insert([
          {
            conversation_id: conversationId,
            sender_id: user.id,
            content: content.trim(),
          }
        ])
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .single()

      if (error) {
        toast.error('Error al enviar mensaje')
        return { error: new Error(error.message) }
      }

      // Agregar el mensaje al estado local
      setMessages(prev => [...prev, data])

      // Actualizar la conversación para que aparezca al principio
      await supabase
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', conversationId)

      return { data, error: null }
    } catch (err) {
      toast.error('Error inesperado al enviar mensaje')
      return { error: err as Error }
    }
  }

  // Marcar mensajes como leídos
  const markMessagesAsRead = async (conversationId: string) => {
    if (!user) return

    try {
      await supabase
        .from('messages')
        .update({ status: 'read' })
        .eq('conversation_id', conversationId)
        .neq('sender_id', user.id)
        .eq('status', 'sent')

      // Actualizar el estado local
      setMessages(prev => prev.map(message => 
        message.sender_id !== user.id && message.status === 'sent'
          ? { ...message, status: 'read' as const }
          : message
      ))
    } catch (err) {
      console.error('Error marking messages as read:', err)
    }
  }

  // Seleccionar conversación actual
  const selectConversation = async (conversation: ConversationWithDetails) => {
    setCurrentConversation(conversation)
    await fetchMessages(conversation.id)
    await markMessagesAsRead(conversation.id)
  }

  // Configurar suscripción en tiempo real para mensajes
  useEffect(() => {
    if (!currentConversation) return

    const subscription = supabase
      .channel(`messages:${currentConversation.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${currentConversation.id}`,
        },
        async (payload) => {
          // Obtener el mensaje completo con información del sender
          const { data: newMessage } = await supabase
            .from('messages')
            .select(`
              *,
              sender:profiles!messages_sender_id_fkey (
                id,
                username,
                full_name,
                avatar_url
              )
            `)
            .eq('id', payload.new.id)
            .single()

          if (newMessage && newMessage.sender_id !== user?.id) {
            setMessages(prev => [...prev, newMessage])
            // Marcar como leído si es de otro usuario
            await markMessagesAsRead(currentConversation.id)
          }
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [currentConversation, user])

  useEffect(() => {
    if (user) {
      fetchConversations()
    }
  }, [user])

  return {
    conversations,
    currentConversation,
    messages,
    loading,
    messagesLoading,
    fetchConversations,
    getOrCreateConversation,
    sendMessage,
    selectConversation,
    markMessagesAsRead,
  }
}
