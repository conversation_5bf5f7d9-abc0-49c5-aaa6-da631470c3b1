import React, { useState, useRef, useEffect } from 'react'
import { ConversationWithDetails, MessageWithSender } from '../../types/database'
import { useAuth } from '../../contexts/AuthContext'
import { Send, User, ArrowLeft } from 'lucide-react'
import { format } from 'date-fns'

interface ChatWindowProps {
  conversation: ConversationWithDetails | null
  messages: MessageWithSender[]
  onSendMessage: (content: string) => Promise<void>
  onBack?: () => void
  loading: boolean
}

export const ChatWindow: React.FC<ChatWindowProps> = ({
  conversation,
  messages,
  onSendMessage,
  onBack,
  loading,
}) => {
  const [newMessage, setNewMessage] = useState('')
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { user } = useAuth()

  // Scroll automático al final cuando llegan nuevos mensajes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  if (!conversation) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Selecciona una conversación
          </h3>
          <p className="text-gray-500">
            Elige una conversación de la lista para comenzar a chatear
          </p>
        </div>
      </div>
    )
  }

  const otherUser = conversation.owner_id === user?.id 
    ? conversation.interested_user 
    : conversation.owner

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || sending) return

    setSending(true)
    try {
      await onSendMessage(newMessage.trim())
      setNewMessage('')
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header del chat */}
      <div className="flex items-center p-4 border-b border-gray-200 bg-white">
        {onBack && (
          <button
            onClick={onBack}
            className="mr-3 p-1 hover:bg-gray-100 rounded-full md:hidden"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
        )}
        
        <div className="flex items-center space-x-3 flex-1">
          {/* Avatar del otro usuario */}
          {otherUser.avatar_url ? (
            <img
              src={otherUser.avatar_url}
              alt={otherUser.full_name}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
              <User className="h-5 w-5 text-gray-400" />
            </div>
          )}

          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">
              {otherUser.full_name}
            </h3>
            <p className="text-sm text-gray-500">
              Conversación sobre: {conversation.pets.name}
            </p>
          </div>

          {/* Información de la mascota */}
          <div className="hidden sm:flex items-center space-x-2">
            {conversation.pets.photo_url && (
              <img
                src={conversation.pets.photo_url}
                alt={conversation.pets.name}
                className="w-8 h-8 rounded object-cover"
              />
            )}
            <span className={`text-xs px-2 py-1 rounded-full ${
              conversation.pets.status === 'lost'
                ? 'bg-red-100 text-red-800'
                : 'bg-green-100 text-green-800'
            }`}>
              {conversation.pets.status === 'lost' ? 'Perdido' : 'Adopción'}
            </span>
          </div>
        </div>
      </div>

      {/* Área de mensajes */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">Cargando mensajes...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-gray-500 mb-2">
                Esta es el inicio de tu conversación con {otherUser.full_name}
              </p>
              <p className="text-sm text-gray-400">
                Envía un mensaje para comenzar
              </p>
            </div>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOwnMessage = message.sender_id === user?.id
            const showDate = index === 0 || 
              format(new Date(messages[index - 1].created_at), 'yyyy-MM-dd') !== 
              format(new Date(message.created_at), 'yyyy-MM-dd')

            return (
              <div key={message.id}>
                {/* Separador de fecha */}
                {showDate && (
                  <div className="flex justify-center my-4">
                    <span className="bg-gray-100 text-gray-500 text-xs px-3 py-1 rounded-full">
                      {format(new Date(message.created_at), 'dd/MM/yyyy')}
                    </span>
                  </div>
                )}

                {/* Mensaje */}
                <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isOwnMessage
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    <p className={`text-xs mt-1 ${
                      isOwnMessage ? 'text-primary-200' : 'text-gray-500'
                    }`}>
                      {format(new Date(message.created_at), 'HH:mm')}
                      {isOwnMessage && (
                        <span className="ml-1">
                          {message.status === 'read' ? '✓✓' : '✓'}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            )
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input para nuevo mensaje */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Escribe un mensaje..."
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            rows={1}
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || sending}
            className="btn-primary px-3 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4" />
          </button>
        </form>
      </div>
    </div>
  )
}
